{# 设备相关组件宏定义文件 #}

{# 导入UI组件宏 #}
{% from 'macros/ui_components.html' import button, status_badge %}

{# 设备参数表格行宏 #}
{% macro parameter_row(param_name, param_address, param_value, param_description, editable=true, disabled=false) %}
<tr>
    <td>{{ param_name }}</td>
    <td>{{ param_address }}</td>
    <td id="{{ param_name }}">{{ param_value if param_value is not none else '--' }}</td>
    <td>{{ param_description }}</td>
    <td>
        {% if editable %}
        {{ button("编辑", 
                  onclick="editParameter('{}', {})".format(param_name, param_address),
                  color="primary",
                  size="sm",
                  icon="edit",
                  disabled=disabled) }}
        {% else %}
        {{ button("编辑", 
                  color="primary",
                  size="sm", 
                  icon="edit",
                  disabled=true) }}
        {% endif %}
    </td>
</tr>
{% endmacro %}

{# 设备参数分类表格宏 #}
{% macro parameter_table(tab_id, tab_name, parameters, icon="list") %}
<div class="tab-pane fade {% if loop.first %}show active{% endif %}" 
     id="{{ tab_id }}" 
     role="tabpanel" 
     aria-labelledby="{{ tab_id }}-tab">
    <div class="table-responsive">
        <table class="table table-hover table-striped">
            <thead class="table-light">
                <tr>
                    <th>参数名称</th>
                    <th>寄存器地址</th>
                    <th>参数值</th>
                    <th>参数说明</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                {% for param in parameters %}
                {{ parameter_row(
                    param.name, 
                    param.address, 
                    param.value, 
                    param.description,
                    param.editable|default(true),
                    param.disabled|default(false)
                ) }}
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endmacro %}

{# 设备参数分类标签宏 #}
{% macro parameter_tabs(categories) %}
<div class="mb-3">
    <ul class="nav nav-pills" id="parameterTabs" role="tablist">
        {% for category in categories %}
        <li class="nav-item" role="presentation">
            <button class="nav-link {% if loop.first %}active{% endif %}" 
                    id="{{ category.id }}-tab" 
                    data-bs-toggle="pill" 
                    data-bs-target="#{{ category.id }}" 
                    type="button" 
                    role="tab" 
                    aria-controls="{{ category.id }}" 
                    aria-selected="{% if loop.first %}true{% else %}false{% endif %}">
                <i class="fas fa-{{ category.icon }} me-1"></i> {{ category.name }}
            </button>
        </li>
        {% endfor %}
    </ul>
</div>
{% endmacro %}

{# 设备操作按钮组宏 #}
{% macro device_action_buttons(device_id) %}
<div class="d-flex flex-wrap gap-2">
    {{ button("查询参数", onclick="queryParameters()", color="primary", icon="sync-alt") }}
    {{ button("查询调试信息", onclick="queryDebugInfo()", color="secondary", icon="bug") }}
    {{ button("查询固件信息", onclick="queryFirmwareInfo()", color="info", icon="microchip") }}
    {{ button("查询错误计数", onclick="queryErrorCounts()", color="warning", icon="exclamation-triangle") }}
    {{ button("查询位置", onclick="queryDeviceLocation()", color="info", icon="map-marker-alt") }}
    {{ button("调试脚本", onclick="openDebugScriptModal()", color="success", icon="code") }}
    {{ button("查询SIM卡信息", onclick="querySimInfo()", color="success", icon="sim-card") }}
</div>
{% endmacro %}

{# 设备历史数据下拉菜单宏 #}
{% macro device_history_dropdown(device_id) %}
<div class="btn-group">
    <button class="btn btn-outline-primary dropdown-toggle" type="button" id="historyDropdown" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="fas fa-chart-line me-1"></i> 历史数据
    </button>
    <ul class="dropdown-menu" aria-labelledby="historyDropdown">
        <li>
            <a class="dropdown-item" href="{{ url_for('debug_script.power_history_page', device_id=device_id) }}">
                <i class="fas fa-bolt me-2 text-warning"></i> 功率
            </a>
        </li>
        <li>
            <a class="dropdown-item" href="{{ url_for('debug_script.temperature_history_page', device_id=device_id) }}">
                <i class="fas fa-thermometer-half me-2 text-danger"></i> 温度
            </a>
        </li>
        <li>
            <a class="dropdown-item" href="{{ url_for('debug_script.voltage_history_page', device_id=device_id) }}">
                <i class="fas fa-plug me-2 text-info"></i> 电压
            </a>
        </li>
        <li>
            <a class="dropdown-item" href="{{ url_for('debug_script.csq_history_page', device_id=device_id) }}">
                <i class="fas fa-signal me-2 text-success"></i> 信号质量
            </a>
        </li>
    </ul>
</div>
{% endmacro %}

{# 设备列表表格行宏 #}
{% macro device_table_row(device) %}
<tr>
    <td>
        <div class="form-check">
            <input class="form-check-input device-checkbox" type="checkbox" value="{{ device.id }}">
        </div>
    </td>
    <td class="device-id-cell">{{ device.device_id }}</td>
    <td class="device-remark-cell">{{ device.device_remark or '无备注' }}</td>
    <td>
        {{ status_badge(device.is_online if device.is_online is defined else false) }}
    </td>
    <td class="product-key-cell">{{ device.product_key }}</td>
    <td>{{ device.device_type_name }}</td>
    <td>{{ device.firmware_version or '未知' }}</td>
    <td>
        {% if device.last_ota_status %}
        <span class="badge bg-{{ 'success' if device.last_ota_status == 'success' else 'danger' }}">
            {{ '成功' if device.last_ota_status == 'success' else '失败' }}
        </span>
        {% else %}
        <span class="badge bg-secondary">未升级</span>
        {% endif %}
    </td>
    <td class="time-cell">
        {% if device.last_ota_time %}
        <i class="far fa-clock"></i> {{ device.last_ota_time.strftime('%Y-%m-%d %H:%M') }}
        {% else %}
        --
        {% endif %}
    </td>
    <td class="time-cell">
        <span data-device-last-check="{{ device.id }}">
            {% if device.last_online_time %}
            <i class="far fa-clock"></i> {{ device.last_online_time.strftime('%Y-%m-%d %H:%M:%S') }}
            {% else %}
            <i class="far fa-clock"></i> 未知
            {% endif %}
        </span>
    </td>
    <td>
        {% if device.debug_status %}
        <span class="badge bg-success">运行中</span>
        {% else %}
        <span class="badge bg-secondary">已停止</span>
        {% endif %}
    </td>
    <td class="action-column">
        <div class="action-buttons-container">
            <a href="{{ url_for('main.ota_device', device_id=device.id) }}" 
               class="action-btn action-btn-upgrade" title="OTA升级">
                <i class="fas fa-rocket"></i> 升级
            </a>
            <a href="{{ url_for('device.edit_device', id=device.id) }}" 
               class="action-btn action-btn-edit" title="编辑设备">
                <i class="fas fa-edit"></i> 编辑
            </a>
            <a href="{{ url_for('device_parameters.device_parameters', id=device.id) }}" 
               class="action-btn action-btn-params" title="设备参数">
                <i class="fas fa-cogs"></i> 参数
            </a>
            <a href="{{ url_for('device_console.device_console', device_id=device.id) }}" 
               class="action-btn action-btn-console" title="设备控制台">
                <i class="fas fa-terminal"></i> 控制台
            </a>
            <a href="{{ url_for('ai.device_analysis', device_id=device.id) }}" 
               class="action-btn action-btn-ai" title="AI分析">
                <i class="fas fa-brain"></i> AI
            </a>
            <button class="action-btn action-btn-delete" 
                    onclick="deleteDevice({{ device.id }})" 
                    title="删除设备">
                <i class="fas fa-trash"></i> 删除
            </button>
        </div>
    </td>
</tr>
{% endmacro %}

{# 设备筛选器宏 #}
{% macro device_filters() %}
<div class="d-flex justify-content-between align-items-center mb-3">
    <div class="d-flex gap-2 flex-wrap align-items-center">
        <!-- 在线状态筛选 -->
        <div class="input-group" style="width: 160px;">
            <span class="input-group-text"><i class="fas fa-wifi"></i></span>
            <select class="form-select" id="statusFilter">
                <option value="all">所有状态</option>
                <option value="online">在线设备</option>
                <option value="offline">离线设备</option>
            </select>
        </div>

        <!-- 固件版本筛选 -->
        <div class="input-group" style="width: 160px;">
            <span class="input-group-text"><i class="fas fa-code-branch"></i></span>
            <input type="text" class="form-control" id="firmwareFilter" placeholder="固件版本...">
        </div>

        <!-- 升级状态筛选 -->
        <div class="input-group" style="width: 160px;">
            <span class="input-group-text"><i class="fas fa-sync-alt"></i></span>
            <select class="form-select" id="otaStatusFilter">
                <option value="all">升级状态</option>
                <option value="success">升级成功</option>
                <option value="failed">升级失败</option>
                <option value="none">未升级</option>
            </select>
        </div>

        <!-- 产品密钥筛选 -->
        <div class="input-group" style="width: 160px;">
            <span class="input-group-text"><i class="fas fa-key"></i></span>
            <input type="text" class="form-control" id="productKeyFilter" placeholder="产品密钥...">
        </div>

        <!-- 高级筛选按钮 -->
        {{ button("高级筛选", onclick="toggleAdvancedFilters()", color="outline-secondary", icon="sliders-h", title="展开更多筛选选项") }}
    </div>

    <!-- 搜索框 -->
    <div class="input-group" style="width: 280px;">
        <input type="text" class="form-control" id="searchInput" placeholder="搜索设备ID或备注...">
        <button class="btn btn-outline-secondary" type="button" title="搜索">
            <i class="fas fa-search"></i>
        </button>
    </div>
</div>
{% endmacro %}
