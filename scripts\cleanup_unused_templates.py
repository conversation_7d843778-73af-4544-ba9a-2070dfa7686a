#!/usr/bin/env python3
"""
清理未使用的模板文件脚本
识别并移除不再使用的HTML模板文件
"""

import os
import sys
import re
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def get_used_templates():
    """获取所有被使用的模板文件"""
    used_templates = set()
    
    # 搜索routes目录下的所有Python文件
    routes_dir = Path("routes")
    for py_file in routes_dir.glob("*.py"):
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 查找render_template调用
            pattern = r"render_template\s*\(\s*['\"]([^'\"]+\.html)['\"]"
            matches = re.findall(pattern, content)
            
            for match in matches:
                used_templates.add(match)
                print(f"  发现使用的模板: {match} (在 {py_file})")
                
        except Exception as e:
            print(f"  ❌ 读取文件 {py_file} 失败: {e}")
    
    # 搜索模板文件中的extends和include
    templates_dir = Path("templates")
    for html_file in templates_dir.rglob("*.html"):
        try:
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找extends
            extends_pattern = r"{% extends ['\"]([^'\"]+\.html)['\"] %}"
            extends_matches = re.findall(extends_pattern, content)
            for match in extends_matches:
                used_templates.add(match)
                print(f"  发现继承的模板: {match} (在 {html_file})")
            
            # 查找include
            include_pattern = r"{% include ['\"]([^'\"]+\.html)['\"] %}"
            include_matches = re.findall(include_pattern, content)
            for match in include_matches:
                used_templates.add(match)
                print(f"  发现包含的模板: {match} (在 {html_file})")
                
            # 查找from导入
            from_pattern = r"{% from ['\"]([^'\"]+\.html)['\"]"
            from_matches = re.findall(from_pattern, content)
            for match in from_matches:
                used_templates.add(match)
                print(f"  发现导入的模板: {match} (在 {html_file})")
                
        except Exception as e:
            print(f"  ❌ 读取模板文件 {html_file} 失败: {e}")
    
    return used_templates

def get_all_templates():
    """获取所有模板文件"""
    all_templates = set()
    templates_dir = Path("templates")
    
    for html_file in templates_dir.rglob("*.html"):
        # 转换为相对路径
        relative_path = html_file.relative_to(templates_dir)
        template_path = str(relative_path).replace('\\', '/')
        all_templates.add(template_path)
    
    return all_templates

def identify_unused_templates():
    """识别未使用的模板文件"""
    print("🔍 正在搜索使用的模板文件...")
    used_templates = get_used_templates()
    
    print("\n📁 正在获取所有模板文件...")
    all_templates = get_all_templates()
    
    print(f"\n📊 统计信息:")
    print(f"  总模板文件数: {len(all_templates)}")
    print(f"  使用的模板数: {len(used_templates)}")
    
    # 找出未使用的模板
    unused_templates = all_templates - used_templates
    
    print(f"\n🗑️  可能未使用的模板文件 ({len(unused_templates)} 个):")
    
    # 按类型分类显示
    old_templates = []
    backup_templates = []
    other_unused = []
    
    for template in sorted(unused_templates):
        if any(keyword in template.lower() for keyword in ['old', 'backup', 'bak', 'temp']):
            backup_templates.append(template)
        elif template in ['index.html', 'devices.html', 'device_parameters.html', 'base.html']:
            old_templates.append(template)
        else:
            other_unused.append(template)
    
    if old_templates:
        print(f"\n  📜 旧版本模板 ({len(old_templates)} 个):")
        for template in old_templates:
            print(f"    - {template}")
    
    if backup_templates:
        print(f"\n  💾 备份/临时模板 ({len(backup_templates)} 个):")
        for template in backup_templates:
            print(f"    - {template}")
    
    if other_unused:
        print(f"\n  ❓ 其他未使用模板 ({len(other_unused)} 个):")
        for template in other_unused:
            print(f"    - {template}")
    
    return {
        'old_templates': old_templates,
        'backup_templates': backup_templates,
        'other_unused': other_unused,
        'all_unused': unused_templates
    }

def create_backup_directory():
    """创建备份目录"""
    backup_dir = Path("templates_backup")
    backup_dir.mkdir(exist_ok=True)
    return backup_dir

def move_to_backup(template_files, backup_dir):
    """将文件移动到备份目录"""
    moved_count = 0
    
    for template in template_files:
        source_path = Path("templates") / template
        if source_path.exists():
            # 创建目标目录结构
            target_path = backup_dir / template
            target_path.parent.mkdir(parents=True, exist_ok=True)
            
            try:
                # 移动文件
                source_path.rename(target_path)
                print(f"  ✅ 已移动: {template}")
                moved_count += 1
            except Exception as e:
                print(f"  ❌ 移动失败 {template}: {e}")
        else:
            print(f"  ⚠️  文件不存在: {template}")
    
    return moved_count

def main():
    """主函数"""
    print("🧹 开始清理未使用的模板文件...")
    print("=" * 60)
    
    # 识别未使用的模板
    unused_info = identify_unused_templates()
    
    if not unused_info['all_unused']:
        print("\n✅ 没有发现未使用的模板文件！")
        return
    
    print(f"\n📋 清理建议:")
    print(f"  1. 旧版本模板可以安全移除（已有新版本）")
    print(f"  2. 备份/临时模板可以移除")
    print(f"  3. 其他未使用模板需要手动确认")
    
    # 询问是否执行清理
    response = input(f"\n❓ 是否要将旧版本模板移动到备份目录？(y/N): ").strip().lower()
    
    if response == 'y':
        backup_dir = create_backup_directory()
        print(f"\n📦 正在移动旧版本模板到 {backup_dir}...")
        
        moved_count = move_to_backup(unused_info['old_templates'], backup_dir)
        
        print(f"\n✅ 清理完成！")
        print(f"  移动了 {moved_count} 个旧版本模板文件")
        print(f"  备份位置: {backup_dir}")
        print(f"  如需恢复，请手动从备份目录复制回来")
        
        # 显示剩余的未使用模板
        remaining = len(unused_info['backup_templates']) + len(unused_info['other_unused'])
        if remaining > 0:
            print(f"\n⚠️  还有 {remaining} 个模板文件需要手动检查")
    else:
        print("\n❌ 取消清理操作")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ 操作被用户取消")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        import traceback
        traceback.print_exc()
