{% extends "base_new.html" %}

{# 导入宏 #}
{% from 'macros/ui_components.html' import page_header, card_container, button, stat_card, modal, form_input, form_select %}
{% from 'macros/device_components.html' import device_filters, device_table_row %}

{% block title %}设备管理{% endblock %}

{% block container_class %}-fluid{% endblock %}

{% block content %}
<div class="mt-4">
    {# 页面标题和统计信息 #}
    {{ page_header("设备管理", "microchip", "管理和监控所有充电桩设备",
        button("添加设备", onclick="$('#addDeviceModal').modal('show')", color="primary", icon="plus") +
        button("批量导入", onclick="$('#batchImportModal').modal('show')", color="success", icon="file-import") +
        button("导出设备", onclick="exportDevices()", color="info", icon="download")
    ) }}

    {# 统计卡片 #}
    <div class="row mb-4">
        {{ stat_card("总设备数", total_devices|default(0), "microchip", "primary", id="totalDevicesCount") }}
        {{ stat_card("在线设备", online_devices|default(0), "wifi", "success", id="onlineDevicesCount") }}
        {{ stat_card("离线设备", offline_devices|default(0), "times-circle", "danger", id="offlineDevicesCount") }}
        {{ stat_card("今日新增", today_new|default(0), "plus-circle", "info") }}
    </div>

    {# 批量操作工具栏 #}
    {% call card_container("批量操作工具", "tools", body_class="py-3") %}
        <div class="row g-2">
            <div class="col-md-6">
                <div class="d-flex flex-wrap gap-2 align-items-center">
                    <span class="badge bg-primary me-2">设备选择</span>
                    {{ button("全选", onclick="selectAllDevices()", color="primary", size="sm", icon="check-square") }}
                    {{ button("取消全选", onclick="unselectAllDevices()", color="outline-primary", size="sm", icon="square") }}
                    {{ button("反选", onclick="invertSelection()", color="outline-primary", size="sm", icon="exchange-alt") }}
                </div>
            </div>
            <div class="col-md-6">
                <div class="d-flex flex-wrap gap-2 align-items-center justify-content-md-end">
                    <span class="badge bg-warning me-2">批量操作</span>
                    {{ button("批量OTA", onclick="batchOta()", color="warning", size="sm", icon="rocket") }}
                    {{ button("批量删除", onclick="batchDelete()", color="danger", size="sm", icon="trash") }}
                    {{ button("导出选中", onclick="exportSelected()", color="info", size="sm", icon="download") }}
                </div>
            </div>
        </div>
        
        <div class="mt-2">
            <small class="text-muted">
                <i class="fas fa-info-circle me-1"></i>
                已选择 <span id="selectedCount" class="fw-bold text-primary">0</span> 个设备
            </small>
        </div>
    {% endcall %}

    {# 设备筛选器 #}
    {% call card_container("设备筛选", "filter") %}
        {{ device_filters() }}
        
        {# 高级筛选选项（默认隐藏） #}
        <div id="advancedFilters" class="mt-3" style="display: none;">
            <div class="row g-2">
                <div class="col-md-3">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                        <input type="date" class="form-control" id="dateFromFilter" placeholder="开始日期">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                        <input type="date" class="form-control" id="dateToFilter" placeholder="结束日期">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                        <input type="text" class="form-control" id="locationFilter" placeholder="设备位置...">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-bug"></i></span>
                        <select class="form-select" id="debugStatusFilter">
                            <option value="all">调试状态</option>
                            <option value="running">运行中</option>
                            <option value="stopped">已停止</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    {% endcall %}

    {# 设备列表表格 #}
    {% call card_container("设备列表", "list") %}
        <div class="table-responsive">
            <table class="table table-hover table-striped" id="devicesTable">
                <thead class="table-light">
                    <tr>
                        <th style="width: 40px;">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()">
                            </div>
                        </th>
                        <th>设备ID</th>
                        <th>设备备注</th>
                        <th>在线状态</th>
                        <th>产品密钥</th>
                        <th>设备类型</th>
                        <th>固件版本</th>
                        <th>升级状态</th>
                        <th>上次升级时间</th>
                        <th>最后在线时间</th>
                        <th>调试状态</th>
                        <th style="width: 200px;">操作</th>
                    </tr>
                </thead>
                <tbody id="devicesTableBody">
                    {% if devices %}
                        {% for device in devices %}
                        {{ device_table_row(device) }}
                        {% endfor %}
                    {% else %}
                    <tr>
                        <td colspan="12" class="text-center text-muted py-4">
                            <i class="fas fa-inbox fa-3x mb-3 d-block"></i>
                            <p class="mb-0">暂无设备数据</p>
                            <small>点击"添加设备"按钮开始添加设备</small>
                        </td>
                    </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
        
        {# 分页控件 #}
        {% if pagination %}
        <div class="d-flex justify-content-between align-items-center mt-3">
            <div class="text-muted">
                显示第 {{ pagination.start }} - {{ pagination.end }} 条，共 {{ pagination.total }} 条记录
            </div>
            <nav aria-label="设备列表分页">
                <ul class="pagination mb-0">
                    <li class="page-item {% if not pagination.has_prev %}disabled{% endif %}">
                        <a class="page-link" href="{% if pagination.has_prev %}{{ url_for('device.devices', page=pagination.prev_num) }}{% else %}#{% endif %}">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                    
                    {% for page_num in pagination.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != pagination.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('device.devices', page=page_num) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    <li class="page-item {% if not pagination.has_next %}disabled{% endif %}">
                        <a class="page-link" href="{% if pagination.has_next %}{{ url_for('device.devices', page=pagination.next_num) }}{% else %}#{% endif %}">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
        {% endif %}
    {% endcall %}
</div>

{# 添加设备模态框 #}
{% call modal("addDeviceModal", "添加设备", size="lg", icon="plus") %}
    <form id="addDeviceForm" method="POST" action="{{ url_for('device.add_device') }}">
        <div class="row">
            <div class="col-md-6">
                {{ form_input("device_id", "设备ID", placeholder="请输入设备ID", icon="microchip", required=true) }}
                {{ form_input("device_remark", "设备备注", placeholder="请输入设备备注", icon="comment") }}
                {{ form_select("product_key", "产品密钥", [
                    ("a1BdWKgzXXX", "充电桩产品"),
                    ("a1CdWKgzYYY", "测试产品")
                ], icon="key", required=true) }}
            </div>
            <div class="col-md-6">
                {{ form_select("device_type", "设备类型", [
                    ("charging_pile", "充电桩"),
                    ("test_device", "测试设备")
                ], icon="cogs", required=true) }}
                {{ form_input("device_secret", "设备密钥", placeholder="请输入设备密钥", icon="lock") }}
                {{ form_input("location", "设备位置", placeholder="请输入设备位置", icon="map-marker-alt") }}
            </div>
        </div>
    </form>
    
    <div class="modal-footer">
        {{ button("取消", type="button", color="secondary", onclick="$('#addDeviceModal').modal('hide')") }}
        {{ button("添加", onclick="submitAddDevice()", color="primary", icon="plus") }}
    </div>
{% endcall %}

{# 批量导入模态框 #}
{% call modal("batchImportModal", "批量导入设备", size="lg", icon="file-import") %}
    <form id="batchImportForm" enctype="multipart/form-data">
        <div class="mb-3">
            <label for="importFile" class="form-label">选择Excel文件</label>
            <input type="file" class="form-control" id="importFile" name="file" accept=".xlsx,.xls" required>
            <div class="form-text">支持.xlsx和.xls格式，文件大小不超过10MB</div>
        </div>
        
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle me-2"></i>Excel文件格式要求：</h6>
            <ul class="mb-0">
                <li>第一行为标题行：设备ID、设备备注、产品密钥、设备类型、设备密钥、设备位置</li>
                <li>从第二行开始为数据行</li>
                <li>设备ID和产品密钥为必填项</li>
            </ul>
        </div>
        
        <div class="mb-3">
            <a href="{{ url_for('static', filename='templates/device_import_template.xlsx') }}" class="btn btn-outline-info btn-sm">
                <i class="fas fa-download me-1"></i>下载模板文件
            </a>
        </div>
    </form>
    
    <div class="modal-footer">
        {{ button("取消", type="button", color="secondary", onclick="$('#batchImportModal').modal('hide')") }}
        {{ button("开始导入", onclick="submitBatchImport()", color="success", icon="file-import") }}
    </div>
{% endcall %}
{% endblock %}

{% block scripts %}
<script>
    // 全局变量
    let selectedDevices = new Set();
    let allDevices = [];
    
    // 页面加载时初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化设备数据
        initializeDeviceData();
        
        // 绑定筛选事件
        bindFilterEvents();
        
        // 启动设备状态更新
        startDeviceStatusUpdates();
        
        // 更新选中计数
        updateSelectedCount();
    });
    
    // 初始化设备数据
    function initializeDeviceData() {
        const rows = document.querySelectorAll('#devicesTableBody tr');
        allDevices = Array.from(rows).map(row => {
            const deviceId = row.querySelector('.device-id-cell')?.textContent;
            return {
                id: deviceId,
                row: row
            };
        }).filter(device => device.id);
    }
    
    // 绑定筛选事件
    function bindFilterEvents() {
        // 状态筛选
        document.getElementById('statusFilter')?.addEventListener('change', applyFilters);
        
        // 固件版本筛选
        document.getElementById('firmwareFilter')?.addEventListener('input', debounce(applyFilters, 300));
        
        // OTA状态筛选
        document.getElementById('otaStatusFilter')?.addEventListener('change', applyFilters);
        
        // 产品密钥筛选
        document.getElementById('productKeyFilter')?.addEventListener('input', debounce(applyFilters, 300));
        
        // 搜索框
        document.getElementById('searchInput')?.addEventListener('input', debounce(applyFilters, 300));
    }
    
    // 应用筛选
    function applyFilters() {
        const statusFilter = document.getElementById('statusFilter')?.value || 'all';
        const firmwareFilter = document.getElementById('firmwareFilter')?.value.toLowerCase() || '';
        const otaStatusFilter = document.getElementById('otaStatusFilter')?.value || 'all';
        const productKeyFilter = document.getElementById('productKeyFilter')?.value.toLowerCase() || '';
        const searchInput = document.getElementById('searchInput')?.value.toLowerCase() || '';
        
        const rows = document.querySelectorAll('#devicesTableBody tr');
        let visibleCount = 0;
        
        rows.forEach(row => {
            const deviceId = row.querySelector('.device-id-cell')?.textContent.toLowerCase() || '';
            const deviceRemark = row.querySelector('.device-remark-cell')?.textContent.toLowerCase() || '';
            const productKey = row.querySelector('.product-key-cell')?.textContent.toLowerCase() || '';
            const statusBadge = row.querySelector('.badge');
            const isOnline = statusBadge?.textContent.includes('在线') || false;
            
            let shouldShow = true;
            
            // 状态筛选
            if (statusFilter === 'online' && !isOnline) shouldShow = false;
            if (statusFilter === 'offline' && isOnline) shouldShow = false;
            
            // 固件版本筛选
            if (firmwareFilter && !row.textContent.toLowerCase().includes(firmwareFilter)) shouldShow = false;
            
            // 产品密钥筛选
            if (productKeyFilter && !productKey.includes(productKeyFilter)) shouldShow = false;
            
            // 搜索筛选
            if (searchInput && !deviceId.includes(searchInput) && !deviceRemark.includes(searchInput)) shouldShow = false;
            
            row.style.display = shouldShow ? '' : 'none';
            if (shouldShow) visibleCount++;
        });
        
        // 更新显示计数
        console.log(`筛选后显示 ${visibleCount} 个设备`);
    }
    
    // 切换高级筛选
    function toggleAdvancedFilters() {
        const advancedFilters = document.getElementById('advancedFilters');
        if (advancedFilters.style.display === 'none') {
            advancedFilters.style.display = 'block';
        } else {
            advancedFilters.style.display = 'none';
        }
    }
    
    // 设备选择相关函数
    function selectAllDevices() {
        const checkboxes = document.querySelectorAll('.device-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
            selectedDevices.add(checkbox.value);
        });
        updateSelectedCount();
    }
    
    function unselectAllDevices() {
        const checkboxes = document.querySelectorAll('.device-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        selectedDevices.clear();
        updateSelectedCount();
    }
    
    function invertSelection() {
        const checkboxes = document.querySelectorAll('.device-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = !checkbox.checked;
            if (checkbox.checked) {
                selectedDevices.add(checkbox.value);
            } else {
                selectedDevices.delete(checkbox.value);
            }
        });
        updateSelectedCount();
    }
    
    function toggleSelectAll() {
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        if (selectAllCheckbox.checked) {
            selectAllDevices();
        } else {
            unselectAllDevices();
        }
    }
    
    function updateSelectedCount() {
        document.getElementById('selectedCount').textContent = selectedDevices.size;
    }
    
    // 批量操作函数
    function batchOta() {
        if (selectedDevices.size === 0) {
            alert('请先选择要升级的设备');
            return;
        }
        
        if (confirm(`确定要对选中的 ${selectedDevices.size} 个设备执行批量OTA升级吗？`)) {
            // 实现批量OTA逻辑
            showNotification('批量OTA功能开发中...', 'info');
        }
    }
    
    function batchDelete() {
        if (selectedDevices.size === 0) {
            alert('请先选择要删除的设备');
            return;
        }
        
        if (confirm(`确定要删除选中的 ${selectedDevices.size} 个设备吗？此操作不可撤销！`)) {
            // 实现批量删除逻辑
            showNotification('批量删除功能开发中...', 'info');
        }
    }
    
    function exportSelected() {
        if (selectedDevices.size === 0) {
            alert('请先选择要导出的设备');
            return;
        }
        
        // 实现导出选中设备逻辑
        showNotification('导出选中设备功能开发中...', 'info');
    }
    
    function exportDevices() {
        // 实现导出所有设备逻辑
        showNotification('导出设备功能开发中...', 'info');
    }
    
    // 添加设备
    function submitAddDevice() {
        const form = document.getElementById('addDeviceForm');
        if (form.checkValidity()) {
            form.submit();
        } else {
            form.reportValidity();
        }
    }
    
    // 批量导入
    function submitBatchImport() {
        const fileInput = document.getElementById('importFile');
        if (!fileInput.files[0]) {
            alert('请选择要导入的Excel文件');
            return;
        }
        
        const formData = new FormData();
        formData.append('file', fileInput.files[0]);
        
        fetch('/api/devices/batch_import', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('批量导入成功', 'success');
                $('#batchImportModal').modal('hide');
                location.reload();
            } else {
                showNotification('批量导入失败: ' + data.error, 'error');
            }
        })
        .catch(error => {
            showNotification('批量导入失败: ' + error, 'error');
        });
    }
    
    // 删除设备
    function deleteDevice(deviceId) {
        if (confirm('确定要删除这个设备吗？此操作不可撤销！')) {
            fetch(`/api/devices/${deviceId}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('设备删除成功', 'success');
                    location.reload();
                } else {
                    showNotification('设备删除失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showNotification('设备删除失败: ' + error, 'error');
            });
        }
    }
    
    // 启动设备状态更新
    function startDeviceStatusUpdates() {
        // 每30秒更新一次设备状态
        setInterval(fetchDeviceStatus, 30000);
    }
    
    // 工具函数
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
</script>
{% endblock %}
