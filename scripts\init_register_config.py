#!/usr/bin/env python3
"""
初始化寄存器配置脚本
将硬编码的寄存器定义迁移到数据库中
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app_factory import create_app
from models.database import db
from models.register_config import RegisterConfig
from iot_client.bin_block.reg_addr import RegAddr

def init_register_configs():
    """初始化寄存器配置数据"""
    app = create_app()
    
    with app.app_context():
        # 检查是否已经有数据
        existing_count = RegisterConfig.query.count()
        if existing_count > 0:
            print(f"寄存器配置表已有 {existing_count} 条记录，跳过初始化")
            return
        
        print("开始初始化寄存器配置...")
        
        # 定义寄存器配置数据
        register_configs = [
            # 时间参数
            {'name': 'REG_T1', 'addr': 0, 'desc': '长时间未插入充电器检测时间(单位：s)', 'category': 'time', 'unit': 's'},
            {'name': 'REG_T2', 'addr': 1, 'desc': '功率大于0连续时间，判定为已连接充电器(单位：s)', 'category': 'time', 'unit': 's'},
            {'name': 'REG_T3', 'addr': 2, 'desc': '浮充时间，大于该时间判定为电量已满(单位：s)', 'category': 'time', 'unit': 's'},
            {'name': 'REG_T4', 'addr': 3, 'desc': '功率超过限制判定时间(单位：s)', 'category': 'time', 'unit': 's'},
            {'name': 'REG_T5', 'addr': 4, 'desc': '总功率超过限制触发时间(单位：ms)', 'category': 'time', 'unit': 'ms'},
            {'name': 'REG_T6', 'addr': 5, 'desc': '温度超过阈值判定时间(单位：s)', 'category': 'time', 'unit': 's'},
            {'name': 'REG_T7', 'addr': 6, 'desc': '初始单个口功率过大判定时间(单位：ms)', 'category': 'time', 'unit': 'ms'},
            {'name': 'REG_T8', 'addr': 7, 'desc': '充电过程中继电器开路状态判断为中控断电的时间(单位：ms)', 'category': 'time', 'unit': 'ms'},
            {'name': 'REG_T9', 'addr': 8, 'desc': '首次进入充电过程中功率突降为0时的浮充时间(单位：s)', 'category': 'time', 'unit': 's'},
            {'name': 'REG_T10', 'addr': 9, 'desc': '无线充电浮充时间(单位：s)', 'category': 'time', 'unit': 's'},
            {'name': 'REG_T11', 'addr': 18, 'desc': '拔出充电器的判定时间(单位：秒)', 'category': 'time', 'unit': 's'},
            
            # 功率参数
            {'name': 'REG_P1', 'addr': 10, 'desc': '浮充功率阈值(单位：W)', 'category': 'power', 'unit': 'W'},
            {'name': 'REG_P2', 'addr': 11, 'desc': '单口充电过程中的功率限制(单位：W)', 'category': 'power', 'unit': 'W'},
            {'name': 'REG_P3', 'addr': 12, 'desc': '单口充电过程中的安全功率限制(单位：W)', 'category': 'power', 'unit': 'W'},
            {'name': 'REG_P4', 'addr': 13, 'desc': '总功率限制(单位：W)', 'category': 'power', 'unit': 'W'},
            {'name': 'REG_P5', 'addr': 14, 'desc': '单口初始安全功率限制(单位：W)', 'category': 'power', 'unit': 'W'},
            {'name': 'REG_P6', 'addr': 15, 'desc': '启动充电后检测充电负载存在阈值(单位：W)', 'category': 'power', 'unit': 'W'},
            {'name': 'REG_P7', 'addr': 16, 'desc': '无线充电浮充功率阈值(单位：W)', 'category': 'power', 'unit': 'W'},
            {'name': 'REG_P8', 'addr': 17, 'desc': '判断是否接入用电设备的阈值，小于这个阈值判定为用电设备断开与插座的连接(单位：V5板子为BL0910的有功功率的寄存器值，V2板子为mW)', 'category': 'power', 'unit': 'mW'},
            
            # 控制和状态参数
            {'name': 'REG_CTRL1', 'addr': 19, 'desc': '控制寄存器，bit0: 控制SIM卡拔出功能，bit1: 控制LED闪烁模式', 'category': 'control', 'data_type': 'hex'},
            {'name': 'REG_TEMP1', 'addr': 20, 'desc': '过温保护阈值(单位：℃)', 'category': 'other', 'unit': '℃'},
            {'name': 'REG_BOOT_CNT', 'addr': 21, 'desc': '启动计数', 'category': 'status', 'data_type': 'integer'},
            {'name': 'REG_VERSION_H', 'addr': 22, 'desc': '版本号高字节', 'category': 'status', 'data_type': 'hex'},
            {'name': 'REG_VERSION_L', 'addr': 23, 'desc': '版本号低字节', 'category': 'status', 'data_type': 'hex'},
            {'name': 'REG_PERSENTAGE', 'addr': 24, 'desc': '拔出插头判定百分比(单位：%)', 'category': 'other', 'unit': '%', 'data_type': 'float'},
            {'name': 'REG_CSQ', 'addr': 25, 'desc': '信号强度(CSQ)和误码率(BER)', 'category': 'status', 'data_type': 'hex'},
            
            # 位置参数
            {'name': 'REG_LOCATION_CODE', 'addr': 26, 'desc': '位置编码', 'category': 'location'},
            {'name': 'REG_LOCATION_LATITUDE_H', 'addr': 27, 'desc': '纬度高字节', 'category': 'location'},
            {'name': 'REG_LOCATION_LATITUDE_L', 'addr': 28, 'desc': '纬度低字节', 'category': 'location'},
            {'name': 'REG_LOCATION_LONGITUDE_H', 'addr': 29, 'desc': '经度高字节', 'category': 'location'},
            {'name': 'REG_LOCATION_LONGITUDE_L', 'addr': 30, 'desc': '经度低字节', 'category': 'location'},
            
            # 错误计数参数
            {'name': 'REG_ERROR_CNT1', 'addr': 31, 'desc': '临时错误计数器1：高字节为服务器掉线次数，低字节为SIM卡被拔出的次数', 'category': 'error'},
            {'name': 'REG_ERROR_CNT2', 'addr': 32, 'desc': '临时错误计数器2：高字节为电压过零中断周期小于工频周期的次数，低字节为电压过零中断周期大于工频周期的次数', 'category': 'error'},
            
            # 安全和保护参数
            {'name': 'REG_UID_PROTECT_KEY1', 'addr': 33, 'desc': 'UID保护密钥1：key1', 'category': 'security'},
            {'name': 'REG_HEART_AND_BILLING_PROTO_TYPE', 'addr': 34, 'desc': 'MQTT服务器的类型', 'category': 'other'},
            {'name': 'REG_RESERV3', 'addr': 35, 'desc': '保留3', 'category': 'other'},
            {'name': 'REG_RESERV4', 'addr': 36, 'desc': '保留4', 'category': 'other'},
            {'name': 'REG_FACTORY_FAULT', 'addr': 37, 'desc': '工厂故障记录1', 'category': 'error'},
            {'name': 'REG_FACTORY_FAULT2', 'addr': 38, 'desc': '工厂故障记录2', 'category': 'error'},
        ]
        
        # 添加插座功率阈值参数
        for plug_num in range(9):  # 插座0-8
            base_addr = 39 + plug_num * 3
            register_configs.extend([
                {'name': f'REG_P2_PLUG{plug_num}', 'addr': base_addr, 'desc': f'插座{plug_num}的P2功率阈值', 'category': 'power', 'unit': 'W'},
                {'name': f'REG_P3_PLUG{plug_num}', 'addr': base_addr + 1, 'desc': f'插座{plug_num}的P3功率阈值', 'category': 'power', 'unit': 'W'},
                {'name': f'REG_P5_PLUG{plug_num}', 'addr': base_addr + 2, 'desc': f'插座{plug_num}的P5功率阈值', 'category': 'power', 'unit': 'W'},
            ])
        
        # 添加其他扩展参数
        register_configs.extend([
            {'name': 'REG_UID_PROTECT_KEY2', 'addr': 66, 'desc': 'UID保护密钥2', 'category': 'security'},
            {'name': 'REG_T12', 'addr': 67, 'desc': '无线充电最大允许充电时间(单位：分钟)', 'category': 'time', 'unit': 'min'},
            {'name': 'REG_WTC_CTRL', 'addr': 68, 'desc': '无线充电控制寄存器', 'category': 'control'},
        ])
        
        # 批量插入数据
        created_count = 0
        for config_data in register_configs:
            try:
                config = RegisterConfig(
                    register_address=config_data['addr'],
                    register_name=config_data['name'],
                    register_alias=config_data['name'],  # 默认别名与名称相同
                    description=config_data['desc'],
                    data_type=config_data.get('data_type', 'integer'),
                    category=config_data.get('category', 'other'),
                    unit=config_data.get('unit', ''),
                    is_active=True
                )
                
                db.session.add(config)
                created_count += 1
                
            except Exception as e:
                print(f"创建寄存器配置失败 {config_data['name']}: {e}")
                continue
        
        try:
            db.session.commit()
            print(f"成功创建 {created_count} 个寄存器配置")
        except Exception as e:
            db.session.rollback()
            print(f"提交数据库失败: {e}")
            return False
        
        return True

if __name__ == '__main__':
    success = init_register_configs()
    if success:
        print("寄存器配置初始化完成！")
    else:
        print("寄存器配置初始化失败！")
        sys.exit(1)
