<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="description" content="OTA设备管理系统 - 高效管理您的设备">
    <meta name="theme-color" content="#007bff">
    <title>{% block title %}OTA设备管理系统{% endblock %}</title>

    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://cdn.bootcdn.net" crossorigin>
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>

    <!-- 基础CSS -->
    {% include 'base_css.html' %}
    
    <!-- 自定义CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    
    <!-- 组件CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/components/back-to-top.css') }}">
    
    <!-- 外部库CSS -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap">
    
    <!-- 页面特定样式 -->
    {% block styles %}{% endblock %}
    
    <!-- 全局样式变量 -->
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --background-color: #f4f6f8;
            --card-background: rgba(255, 255, 255, 0.9);
        }

        body {
            padding-top: 70px;
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, var(--background-color) 0%, #e9ecef 100%);
            color: #333;
            min-height: 100vh;
        }

        /* 导航栏样式 */
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5em;
            color: var(--primary-color) !important;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }
        
        .nav-link {
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .nav-link:hover {
            color: #0056b3 !important;
        }

        /* 卡片样式 */
        .card {
            border: none;
            border-radius: 15px;
            background: var(--card-background);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }
        
        .card-header {
            border-bottom: none;
            border-radius: 15px 15px 0 0 !important;
            background: transparent;
        }

        /* 按钮样式 */
        .btn {
            border-radius: 30px;
            font-weight: 500;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
        }
        
        .btn:disabled {
            opacity: 0.65;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }

        /* 表格样式 */
        .table {
            background: var(--card-background);
            border-radius: 15px;
            overflow: hidden;
        }
        
        .table-hover tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.1);
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        /* 页脚样式 */
        .footer {
            margin-top: 50px;
            padding: 20px 0;
            border-top: 1px solid #e5e5e5;
            text-align: center;
            background: #ffffff;
        }

        /* 深色模式样式 */
        body.dark-mode {
            --background-color: #1a1a1a;
            --card-background: rgba(30, 30, 30, 0.9);
            --primary-color: #4dabf7;
            background: linear-gradient(135deg, var(--background-color) 0%, #2d2d2d 100%);
            color: #e9ecef;
        }
        
        body.dark-mode .navbar {
            background: rgba(30, 30, 30, 0.95) !important;
        }
        
        body.dark-mode .card {
            background: var(--card-background);
            color: #e9ecef;
        }
        
        body.dark-mode .table {
            background: var(--card-background);
            color: #e9ecef;
        }
    </style>
    
    {% block head %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('main.index') }}">OTA设备管理系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div id="navbar" class="collapse navbar-collapse">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.index') }}">
                            <i class="fas fa-home"></i> 首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('device.devices') }}">
                            <i class="fas fa-microchip"></i> 设备管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('device_location.device_map') }}">
                            <i class="fas fa-map-marker-alt"></i> 设备地图
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('firmware.firmware_list') }}">
                            <i class="fas fa-file-code"></i> 固件管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.ota_tasks') }}">
                            <i class="fas fa-tasks"></i> OTA任务
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('batch_ota.batch_ota_page') }}">
                            <i class="fas fa-rocket"></i> 批量OTA
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auto_ota.auto_ota_page') }}">
                            <i class="fas fa-magic"></i> 自动OTA
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('iot.iot_control') }}">
                            <i class="fas fa-server"></i> IoT客户端控制
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="moreDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-h"></i> 更多功能
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('monitor.dashboard') }}">
                                <i class="fas fa-desktop"></i> 服务器监控</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('game.game_list') }}">
                                <i class="fas fa-gamepad"></i> 小游戏中心</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('tools.serial') }}">
                                <i class="fas fa-terminal"></i> Web串口工具</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('tools.toolbox') }}">
                                <i class="fas fa-toolbox"></i> 工具箱</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('register_config.register_config_list') }}">
                                <i class="fas fa-cogs"></i> 寄存器配置</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('model_viewer.model_viewer') }}">
                                <i class="fas fa-cube"></i> 3D模型预览</a></li>
                            <li class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('ai.ai_dashboard') }}">
                                <i class="fas fa-brain"></i> AI智能分析</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('ai.chat') }}">
                                <i class="fas fa-robot"></i> AI助手</a></li>
                            <li><a class="dropdown-item" href="https://liuyuanlin.eu.org/" target="_blank">
                                <i class="fas fa-blog"></i> 个人博客</a></li>
                            <li><a class="dropdown-item" href="https://github.com/MisakaMikoto128" target="_blank">
                                <i class="fab fa-github"></i> GitHub</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('user.change_password') }}">
                                <i class="fas fa-key"></i> 修改密码</a></li>
                            {% if current_user.is_admin %}
                            <li><a class="dropdown-item" href="{{ url_for('user.user_list') }}">
                                <i class="fas fa-users"></i> 用户管理</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('login_logs.login_logs') }}">
                                <i class="fas fa-history"></i> 登录日志</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('paid_download.list_downloads') }}">
                                <i class="fas fa-download"></i> 付费下载</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('paid_download.order_list') }}">
                                <i class="fas fa-shopping-cart"></i> 我的订单</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('merchant.merchant_list') }}">
                                <i class="fas fa-store"></i> 商户管理</a></li>
                            {% endif %}
                        </ul>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    {% if not current_user.is_authenticated %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.login') }}">登录</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.register') }}">注册</a>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.logout') }}">
                            <i class="fas fa-sign-out-alt"></i> 退出
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container{% block container_class %}{% endblock %}">
        <!-- 消息提示 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
        {% for category, message in messages %}
        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {% endfor %}
        {% endif %}
        {% endwith %}

        <!-- 页面内容 -->
        {% block content %}{% endblock %}
    </div>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <p class="text-muted">© 2025 OTA设备管理系统</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='libs/js/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='libs/js/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/theme-switcher.js') }}"></script>
    
    <!-- 全局通知组件 -->
    {% include 'components/notification.html' %}
    
    <!-- 回到顶部组件 -->
    <script src="{{ url_for('static', filename='js/components/back-to-top.js') }}" defer></script>
    
    <!-- 页面特定脚本 -->
    {% block scripts %}{% endblock %}
    
    <!-- 全局脚本 -->
    <script>
        // 设备状态查询间隔（毫秒）
        var statusCheckInterval = 3000;
        
        // 获取设备状态
        function fetchDeviceStatus() {
            fetch('/api/device_status')
                .then(response => response.json())
                .then(data => {
                    // 更新设备状态显示
                    Object.entries(data).forEach(([deviceId, status]) => {
                        const statusBadge = document.querySelector(`[data-device-status="${deviceId}"]`);
                        if (statusBadge) {
                            const isOnline = status.is_online;
                            statusBadge.innerHTML = isOnline ?
                                '<i class="fas fa-circle me-1"></i>在线' :
                                '<i class="fas fa-circle me-1"></i>离线';
                            statusBadge.className = isOnline ?
                                'badge bg-success-subtle text-success' :
                                'badge bg-danger-subtle text-danger';
                        }
                        
                        const lastCheckSpan = document.querySelector(`[data-device-last-check="${deviceId}"]`);
                        if (lastCheckSpan && status.is_online) {
                            const now = new Date();
                            const formattedTime = now.getFullYear() + '-' +
                                String(now.getMonth() + 1).padStart(2, '0') + '-' +
                                String(now.getDate()).padStart(2, '0') + ' ' +
                                String(now.getHours()).padStart(2, '0') + ':' +
                                String(now.getMinutes()).padStart(2, '0') + ':' +
                                String(now.getSeconds()).padStart(2, '0');
                            lastCheckSpan.innerHTML = `<i class="far fa-clock me-1"></i>${formattedTime}`;
                        }
                    });
                    
                    // 更新统计数据
                    const onlineCount = Object.values(data).filter(status => status.is_online).length;
                    const totalCount = Object.keys(data).length;
                    
                    const onlineDevicesCount = document.querySelector('#onlineDevicesCount');
                    if (onlineDevicesCount) {
                        onlineDevicesCount.textContent = onlineCount;
                    }
                    
                    const offlineDevicesCount = document.querySelector('#offlineDevicesCount');
                    if (offlineDevicesCount) {
                        offlineDevicesCount.textContent = totalCount - onlineCount;
                    }
                })
                .catch(error => {
                    console.error('获取设备状态失败:', error);
                });
        }
        
        // 页面加载时启动状态查询
        document.addEventListener('DOMContentLoaded', function() {
            fetchDeviceStatus();
            
            // 添加波纹效果
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const ripple = document.createElement('span');
                    ripple.classList.add('ripple');
                    
                    const rect = button.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    
                    ripple.style.width = ripple.style.height = `${size}px`;
                    ripple.style.left = `${e.clientX - rect.left - size/2}px`;
                    ripple.style.top = `${e.clientY - rect.top - size/2}px`;
                    
                    button.appendChild(ripple);
                    
                    ripple.addEventListener('animationend', () => {
                        ripple.remove();
                    });
                });
            });
        });
    </script>
</body>
</html>
