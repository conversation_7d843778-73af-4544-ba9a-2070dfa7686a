{% extends "base.html" %}

{% block title %}批量OTA升级{% endblock %}

{% block styles %}
<style>
    /* 批量OTA页面样式 - 与设备管理页面保持一致 */
    .filter-card {
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        margin-bottom: 1rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: all 0.2s ease;
    }

    .filter-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        transform: translateY(-1px);
    }

    .filter-header {
        background-color: #f8f9fa;
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #dee2e6;
        font-weight: 600;
        color: #495057;
    }

    .filter-body {
        padding: 1rem;
    }

    /* 设备表格样式 - 与设备管理页面一致 */
    .device-table {
        margin-bottom: 0;
        font-size: 0.9rem;
    }

    .device-table th,
    .device-table td {
        white-space: nowrap;
        vertical-align: middle;
        padding: 0.75rem 0.5rem;
        border-bottom: 1px solid #f0f0f0;
    }

    .device-table th {
        font-weight: 600;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        color: #6c757d;
        background-color: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .device-table tbody tr:hover {
        background-color: #f8f9fa;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: all 0.2s ease;
    }

    .table-container {
        max-height: 500px;
        overflow-y: auto;
        border-radius: 0.375rem;
        border: 1px solid #dee2e6;
    }

    /* 统计卡片样式 - 与设备管理页面一致 */
    .stats-card-primary {
        border-color: #0d6efd;
        background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%);
        color: white;
        border: none;
    }

    .stats-card-success {
        border-color: #198754;
        background: linear-gradient(135deg, #198754 0%, #146c43 100%);
        color: white;
        border: none;
    }

    .stats-card-warning {
        border-color: #ffc107;
        background: linear-gradient(135deg, #ffc107 0%, #ffca2c 100%);
        color: #212529;
        border: none;
    }

    .stats-card-info {
        border-color: #0dcaf0;
        background: linear-gradient(135deg, #0dcaf0 0%, #31d2f2 100%);
        color: #212529;
        border: none;
    }

    /* 按钮样式优化 */
    .btn-filter {
        background-color: #6c757d;
        border-color: #6c757d;
        color: white;
        transition: all 0.2s ease;
    }

    .btn-filter:hover {
        background-color: #5a6268;
        border-color: #545b62;
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .selected-count {
        background-color: #28a745;
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.875rem;
        margin-left: 0.5rem;
        font-weight: 600;
    }

    /* 状态徽章样式 */
    .status-online {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
    }

    .status-offline {
        background: linear-gradient(45deg, #dc3545, #e74c3c);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
    }

    /* 产品密钥样式 */
    .product-key-cell {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 0.8rem;
        background-color: #f8f9fa;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        border: 1px solid #e9ecef;
        color: #495057;
    }

    /* 设备ID样式 */
    .device-id-cell {
        font-weight: 600;
        color: #0d6efd;
    }

    /* 暗黑模式适配 */
    body.dark-mode .filter-card {
        background-color: #2d3748;
        border: 1px solid #4a5568;
    }

    body.dark-mode .filter-header {
        background-color: #1a202c;
        border-bottom: 1px solid #4a5568;
        color: #e2e8f0;
    }

    body.dark-mode .device-table {
        background-color: #2d3748;
        color: #e2e8f0;
    }

    body.dark-mode .device-table th {
        background-color: #1a202c;
        color: #a0aec0;
        border-bottom: 2px solid #4a5568;
    }

    body.dark-mode .device-table td {
        border-bottom: 1px solid #4a5568;
    }

    body.dark-mode .device-table tbody tr:hover {
        background-color: #2d3748;
        box-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    body.dark-mode .product-key-cell {
        background-color: #1a202c;
        border: 1px solid #4a5568;
        color: #e2e8f0;
    }

    body.dark-mode .device-id-cell {
        color: #63b3ed;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- 页面标题和操作按钮 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center flex-wrap gap-3">
                <h2 class="mb-0"><i class="fas fa-rocket text-primary"></i> 批量OTA升级</h2>
                <div class="d-flex gap-2 flex-wrap">
                    <button type="button" class="btn btn-outline-secondary" onclick="resetFilters()">
                        <i class="fas fa-undo me-1"></i>重置筛选
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="refreshDeviceList()">
                        <i class="fas fa-sync-alt me-1"></i>刷新设备
                    </button>
                    <button type="button" class="btn btn-success" id="startBatchOtaBtn" disabled>
                        <i class="fas fa-play me-1"></i>启动批量升级
                        <span class="selected-count" id="selectedCount">0</span>
                    </button>
                </div>
            </div>
            <p class="text-muted mt-2">高级设备筛选器，支持多维度筛选并批量执行OTA升级任务</p>
        </div>
    </div>

    <!-- 统计卡片 - 与设备管理页面风格一致 -->
    <div class="row mb-4" id="statsCards">
        <div class="col-md-3">
            <div class="card stats-card-primary h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-server fa-3x text-white"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="card-title text-white-50 mb-1">设备总数</h6>
                            <h3 class="mb-0 text-white">{{ stats.total_devices }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card-info h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-layer-group fa-3x"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="card-title mb-1" style="color: rgba(33, 37, 41, 0.7);">设备类型</h6>
                            <h3 class="mb-0">{{ stats.device_types|length }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card-warning h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-key fa-3x"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="card-title mb-1" style="color: rgba(33, 37, 41, 0.7);">产品密钥</h6>
                            <h3 class="mb-0">{{ stats.product_keys|length }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card-success h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-filter fa-3x text-white"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="card-title text-white-50 mb-1">筛选结果</h6>
                            <h3 class="mb-0 text-white" id="filteredCount">{{ stats.total_devices }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 筛选条件 -->
        <div class="col-md-4">
            <div class="filter-card">
                <div class="filter-header">
                    <i class="fas fa-filter me-2"></i>筛选条件
                </div>
                <div class="filter-body">
                    <!-- 设备类型筛选 -->
                    <div class="mb-3">
                        <label class="form-label">设备类型</label>
                        <div class="form-check-group">
                            {% for type_name, count in stats.device_types.items() %}
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="device_types" value="{{ type_name }}" id="type_{{ loop.index }}">
                                <label class="form-check-label" for="type_{{ loop.index }}">
                                    {{ type_name }} <span class="text-muted">({{ count }})</span>
                                </label>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- 产品密钥筛选 -->
                    <div class="mb-3">
                        <label class="form-label">产品密钥</label>
                        <select class="form-select" name="product_keys" multiple size="4">
                            {% for key in stats.product_keys %}
                            <option value="{{ key }}">{{ key }}</option>
                            {% endfor %}
                        </select>
                        <small class="form-text text-muted">按住Ctrl可多选</small>
                    </div>

                    <!-- 固件版本筛选 -->
                    <div class="mb-3">
                        <label class="form-label">固件版本</label>
                        <select class="form-select" name="firmware_versions" multiple size="3" id="firmwareVersionSelect">
                            <option value="">加载中...</option>
                        </select>
                        <small class="form-text text-muted">按住Ctrl可多选</small>
                    </div>

                    <!-- 关键字筛选 -->
                    <div class="mb-3">
                        <label class="form-label">包含关键字</label>
                        <input type="text" class="form-control" name="keywords" placeholder="设备ID或备注，多个用逗号分隔">
                        <small class="form-text text-muted">匹配设备ID或设备备注</small>
                    </div>

                    <!-- 排除关键字 -->
                    <div class="mb-3">
                        <label class="form-label">排除关键字</label>
                        <input type="text" class="form-control" name="exclude_keywords" placeholder="设备ID或备注，多个用逗号分隔">
                        <small class="form-text text-muted">排除包含这些关键字的设备</small>
                    </div>

                    <!-- 筛选按钮 -->
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-filter" id="filterBtn">
                            <i class="fas fa-search me-1"></i>应用筛选
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="resetBtn">
                            <i class="fas fa-undo me-1"></i>重置筛选
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设备列表 -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>设备列表
                    </h5>
                    <div>
                        <button type="button" class="btn btn-sm btn-outline-primary" id="selectAllBtn">全选</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="selectNoneBtn">取消全选</button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive table-container">
                        <table class="table table-hover device-table mb-0">
                            <thead>
                                <tr>
                                    <th width="50" class="text-center">
                                        <input type="checkbox" id="selectAllCheckbox" title="全选/取消全选">
                                    </th>
                                    <th><i class="fas fa-microchip me-1"></i>设备ID</th>
                                    <th><i class="fas fa-comment me-1"></i>设备备注</th>
                                    <th><i class="fas fa-tag me-1"></i>设备类型</th>
                                    <th><i class="fas fa-key me-1"></i>产品密钥</th>
                                    <th><i class="fas fa-code-branch me-1"></i>固件版本</th>
                                    <th><i class="fas fa-clock me-1"></i>上次升级</th>
                                    <th><i class="fas fa-signal me-1"></i>设备状态</th>
                                </tr>
                            </thead>
                            <tbody id="deviceTableBody">
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-4">
                                        <i class="fas fa-info-circle me-2"></i>请使用左侧筛选条件来查找设备
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 批量升级确认模态框 -->
<div class="modal fade" id="batchOtaModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认批量OTA升级</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>您即将对 <strong id="confirmDeviceCount">0</strong> 台设备执行批量OTA升级。</p>
                <p class="text-muted">系统将：</p>
                <ul class="text-muted">
                    <li>检查IoT管理器状态</li>
                    <li>根据设备类型自动选择最新固件</li>
                    <li>为每台设备创建OTA升级任务</li>
                    <li>按顺序执行升级，避免系统负载过高</li>
                </ul>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    请确保设备在线且网络连接稳定，升级过程中请勿关闭页面。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmBatchOtaBtn">
                    <i class="fas fa-rocket me-1"></i>确认启动
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let allDevices = [];
let filteredDevices = [];
let selectedDevices = [];

$(document).ready(function() {
    // 加载固件版本列表
    loadFirmwareVersions();

    // 绑定事件
    $('#filterBtn').click(filterDevices);
    $('#resetBtn').click(resetFilters);
    $('#selectAllBtn').click(selectAllDevices);
    $('#selectNoneBtn').click(selectNoneDevices);
    $('#selectAllCheckbox').change(toggleAllDevices);
    $('#startBatchOtaBtn').click(showBatchOtaModal);
    $('#confirmBatchOtaBtn').click(startBatchOta);

    // 初始加载所有设备
    filterDevices();
});

function loadFirmwareVersions() {
    $.get('/api/batch_ota/get_firmware_versions')
        .done(function(response) {
            if (response.success) {
                const select = $('#firmwareVersionSelect');
                select.empty();
                response.versions.forEach(function(version) {
                    select.append(`<option value="${version}">${version}</option>`);
                });
            }
        })
        .fail(function() {
            $('#firmwareVersionSelect').html('<option value="">加载失败</option>');
        });
}

function filterDevices() {
    const filterData = {
        device_types: $('input[name="device_types"]:checked').map(function() {
            return $(this).val();
        }).get(),
        product_keys: $('select[name="product_keys"]').val() || [],
        firmware_versions: $('select[name="firmware_versions"]').val() || [],
        keywords: $('input[name="keywords"]').val(),
        exclude_keywords: $('input[name="exclude_keywords"]').val()
    };

    $.ajax({
        url: '/api/batch_ota/filter_devices',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(filterData),
        success: function(response) {
            if (response.success) {
                filteredDevices = response.devices;
                updateDeviceTable();
                updateFilteredCount(response.total);
            } else {
                showAlert('筛选失败: ' + response.message, 'danger');
            }
        },
        error: function() {
            showAlert('筛选请求失败，请重试', 'danger');
        }
    });
}

function updateDeviceTable() {
    const tbody = $('#deviceTableBody');
    tbody.empty();

    if (filteredDevices.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="8" class="text-center text-muted py-4">
                    <i class="fas fa-search me-2"></i>没有找到符合条件的设备
                </td>
            </tr>
        `);
        return;
    }

    filteredDevices.forEach(function(device) {
        const isSelected = selectedDevices.includes(device.id);
        const row = `
            <tr>
                <td>
                    <input type="checkbox" class="device-checkbox" value="${device.id}" ${isSelected ? 'checked' : ''}>
                </td>
                <td><strong>${device.device_id}</strong></td>
                <td>${device.device_remark}</td>
                <td>
                    <span class="badge bg-info">${device.device_type_name}</span>
                </td>
                <td><small>${device.product_key}</small></td>
                <td>${device.firmware_version}</td>
                <td><small>${device.last_ota_time}</small></td>
                <td>
                    <span class="badge ${device.last_ota_status === '成功' ? 'bg-success' :
                                       device.last_ota_status === '失败' ? 'bg-danger' : 'bg-secondary'}">
                        ${device.last_ota_status}
                    </span>
                </td>
            </tr>
        `;
        tbody.append(row);
    });

    // 绑定复选框事件
    $('.device-checkbox').change(updateSelectedDevices);
}

function updateSelectedDevices() {
    selectedDevices = $('.device-checkbox:checked').map(function() {
        return parseInt($(this).val());
    }).get();

    updateSelectedCount();
    updateSelectAllCheckbox();
}

function updateSelectedCount() {
    const count = selectedDevices.length;
    $('#selectedCount').text(count);
    $('#startBatchOtaBtn').prop('disabled', count === 0);
}

function updateFilteredCount(count) {
    $('#filteredCount').text(count);
}

function updateSelectAllCheckbox() {
    const totalCheckboxes = $('.device-checkbox').length;
    const checkedCheckboxes = $('.device-checkbox:checked').length;

    if (checkedCheckboxes === 0) {
        $('#selectAllCheckbox').prop('indeterminate', false).prop('checked', false);
    } else if (checkedCheckboxes === totalCheckboxes) {
        $('#selectAllCheckbox').prop('indeterminate', false).prop('checked', true);
    } else {
        $('#selectAllCheckbox').prop('indeterminate', true);
    }
}

function selectAllDevices() {
    $('.device-checkbox').prop('checked', true);
    updateSelectedDevices();
}

function selectNoneDevices() {
    $('.device-checkbox').prop('checked', false);
    updateSelectedDevices();
}

function toggleAllDevices() {
    const isChecked = $('#selectAllCheckbox').prop('checked');
    $('.device-checkbox').prop('checked', isChecked);
    updateSelectedDevices();
}

function resetFilters() {
    $('input[name="device_types"]').prop('checked', false);
    $('select[name="product_keys"]').val([]);
    $('select[name="firmware_versions"]').val([]);
    $('input[name="keywords"]').val('');
    $('input[name="exclude_keywords"]').val('');
    filterDevices();
}

function showBatchOtaModal() {
    $('#confirmDeviceCount').text(selectedDevices.length);
    $('#batchOtaModal').modal('show');
}

function startBatchOta() {
    if (selectedDevices.length === 0) {
        showAlert('请选择要升级的设备', 'warning');
        return;
    }

    $('#confirmBatchOtaBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>启动中...');

    $.ajax({
        url: '/api/batch_ota/start',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            device_ids: selectedDevices
        }),
        success: function(response) {
            $('#batchOtaModal').modal('hide');
            if (response.success) {
                showAlert(response.message, 'success');
                if (response.details && response.details.length > 0) {
                    console.log('批量OTA详情:', response.details);
                }
            } else {
                showAlert(response.message, 'danger');
            }
        },
        error: function() {
            showAlert('启动批量OTA失败，请重试', 'danger');
        },
        complete: function() {
            $('#confirmBatchOtaBtn').prop('disabled', false).html('<i class="fas fa-rocket me-1"></i>确认启动');
        }
    });
}

function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // 移除现有的alert
    $('.alert').remove();

    // 添加新的alert到页面顶部
    $('body').prepend(alertHtml);

    // 3秒后自动消失
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 3000);
}
</script>
{% endblock %}
