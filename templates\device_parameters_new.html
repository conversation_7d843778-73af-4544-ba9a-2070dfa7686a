{% extends "base_new.html" %}

{# 导入宏 #}
{% from 'macros/ui_components.html' import page_header, card_container, button, loading_indicator, modal %}
{% from 'macros/device_components.html' import device_action_buttons, device_history_dropdown, parameter_tabs, parameter_table %}

{% block title %}设备参数 - {{ device.device_id }}{% endblock %}

{% block container_class %}-fluid{% endblock %}

{% block content %}
<div class="mt-4">
    {# 页面标题 #}
    {{ page_header("设备参数 - " + device.device_id, "microchip", "",
        '<span class="badge rounded-pill bg-primary-subtle text-primary me-2"><i class="fas fa-info-circle me-1"></i> 设备参数: <span id="param-count">' + (parameters|length if parameters else 0)|string + '</span></span>'
    ) }}

    {# 操作按钮区域 #}
    <div class="row mb-4">
        <div class="col-md-8">
            {{ device_action_buttons(device.id) }}
        </div>
        <div class="col-md-4">
            <div class="d-flex justify-content-md-end mt-3 mt-md-0">
                {{ button("导出参数", id="exportParametersBtn", color="success", icon="download") }}
                {{ device_history_dropdown(device.id) }}
            </div>
        </div>
    </div>

    {# 参数表格卡片 #}
    {% call card_container("设备参数", "list") %}
        {# 参数分类标签 #}
        {{ parameter_tabs([
            {'id': 'all', 'name': '全部', 'icon': 'list'},
            {'id': 'time', 'name': '时间参数', 'icon': 'clock'},
            {'id': 'power', 'name': '功率参数', 'icon': 'bolt'},
            {'id': 'other', 'name': '其他参数', 'icon': 'sliders-h'}
        ]) }}

        {# 参数表格内容 #}
        <div class="tab-content" id="parameterTabsContent">
            {# 全部参数 #}
            <div class="tab-pane fade show active" id="all" role="tabpanel" aria-labelledby="all-tab">
                <div class="table-responsive">
                    <table class="table table-hover table-striped">
                        <thead class="table-light">
                            <tr>
                                <th>参数名称</th>
                                <th>寄存器地址</th>
                                <th>参数值</th>
                                <th>参数说明</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="allParametersTable">
                            {% if parameters %}
                                {% for param in parameters %}
                                <tr>
                                    <td>{{ param.name }}</td>
                                    <td>{{ param.address }}</td>
                                    <td id="{{ param.name }}">{{ param.value if param.value is not none else '--' }}</td>
                                    <td>{{ param.description or '' }}</td>
                                    <td>
                                        {% set is_readonly = param.name in ['REG_BOOT_CNT', 'REG_VERSION_H', 'REG_VERSION_L'] %}
                                        {{ button("编辑", 
                                                  onclick="editParameter('{}', {})".format(param.name, param.address),
                                                  color="primary",
                                                  size="sm",
                                                  icon="edit",
                                                  disabled=is_readonly) }}
                                    </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="5" class="text-center text-muted">
                                        <i class="fas fa-info-circle me-2"></i>暂无参数数据，请先查询参数
                                    </td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>

            {# 时间参数 #}
            <div class="tab-pane fade" id="time" role="tabpanel" aria-labelledby="time-tab">
                <div class="table-responsive">
                    <table class="table table-hover table-striped">
                        <thead class="table-light">
                            <tr>
                                <th>参数名称</th>
                                <th>寄存器地址</th>
                                <th>参数值</th>
                                <th>参数说明</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="timeParametersTable">
                            {# 时间参数将通过JavaScript动态填充 #}
                        </tbody>
                    </table>
                </div>
            </div>

            {# 功率参数 #}
            <div class="tab-pane fade" id="power" role="tabpanel" aria-labelledby="power-tab">
                <div class="table-responsive">
                    <table class="table table-hover table-striped">
                        <thead class="table-light">
                            <tr>
                                <th>参数名称</th>
                                <th>寄存器地址</th>
                                <th>参数值</th>
                                <th>参数说明</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="powerParametersTable">
                            {# 功率参数将通过JavaScript动态填充 #}
                        </tbody>
                    </table>
                </div>
            </div>

            {# 其他参数 #}
            <div class="tab-pane fade" id="other" role="tabpanel" aria-labelledby="other-tab">
                <div class="table-responsive">
                    <table class="table table-hover table-striped">
                        <thead class="table-light">
                            <tr>
                                <th>参数名称</th>
                                <th>寄存器地址</th>
                                <th>参数值</th>
                                <th>参数说明</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="otherParametersTable">
                            {# 其他参数将通过JavaScript动态填充 #}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    {% endcall %}
</div>

{# 编辑参数模态框 #}
{% call modal("editParameterModal", "编辑参数", icon="edit") %}
    <form id="editParameterForm">
        <input type="hidden" id="paramName" name="paramName">
        <input type="hidden" id="paramAddr" name="paramAddr">
        
        <div class="mb-3">
            <label for="paramValue" class="form-label">参数值</label>
            <div class="input-group">
                <span class="input-group-text"><i class="fas fa-edit"></i></span>
                <input type="number" class="form-control" id="paramValue" name="paramValue" required>
            </div>
            <div class="form-text">请输入新的参数值</div>
        </div>
        
        <div class="mb-3">
            <label class="form-label">参数描述</label>
            <p class="form-control-plaintext" id="paramDescription"></p>
        </div>
    </form>
    
    <div class="modal-footer">
        {{ button("取消", type="button", color="secondary", onclick="$('#editParameterModal').modal('hide')") }}
        {{ button("保存", onclick="saveParameter()", color="primary", icon="save") }}
    </div>
{% endcall %}

{# 调试脚本模态框 #}
{% include 'components/debug_script_modal.html' %}
{% endblock %}

{% block scripts %}
<script>
    // 参数数据（从后端传递）
    const parameterData = {{ parameter_data|tojson if parameter_data else '{}' }};
    
    // 参数分类定义（从数据库获取或使用默认分类）
    const timeParams = ['REG_T1', 'REG_T2', 'REG_T3', 'REG_T4', 'REG_T5', 'REG_T6', 'REG_T7', 'REG_T8', 'REG_T9', 'REG_T10', 'REG_T11', 'REG_T12'];
    const powerParams = ['REG_P1', 'REG_P2', 'REG_P3', 'REG_P4', 'REG_P5', 'REG_P6', 'REG_P7', 'REG_P8'];
    
    // 页面加载时初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 首先尝试从数据库获取保存的参数
        fetchSavedParameters();
        
        // 初始化参数分类
        initializeParameterCategories();
        
        // 更新参数计数
        updateParameterCount();
        
        // 添加标签切换事件监听
        document.querySelectorAll('#parameterTabs .nav-link').forEach(tab => {
            tab.addEventListener('shown.bs.tab', function(event) {
                updateParameterCategories();
            });
        });
    });
    
    // 获取保存的参数
    function fetchSavedParameters() {
        const paramElements = document.querySelectorAll('[id^="REG_"]');
        paramElements.forEach(el => {
            el.textContent = '加载中...';
        });
        
        fetch('/api/device/{{ device.id }}/saved_parameters')
            .then(response => response.json())
            .then(data => {
                for (const [key, paramData] of Object.entries(data)) {
                    const elements = document.querySelectorAll(`[id="${key}"]`);
                    elements.forEach(element => {
                        if (element) {
                            if (typeof paramData === 'object' && paramData !== null && 'value' in paramData) {
                                element.textContent = paramData.value;
                            } else {
                                element.textContent = paramData;
                            }
                        }
                    });
                }
                updateParameterCategories();
            })
            .catch(error => {
                console.error('获取保存的参数失败:', error);
                paramElements.forEach(el => {
                    el.textContent = '--';
                });
            });
    }
    
    // 初始化参数分类
    function initializeParameterCategories() {
        updateParameterCategories();
    }
    
    // 更新参数分类显示
    function updateParameterCategories() {
        const allRows = document.querySelectorAll('#allParametersTable tr');
        
        // 清空分类表格
        document.getElementById('timeParametersTable').innerHTML = '';
        document.getElementById('powerParametersTable').innerHTML = '';
        document.getElementById('otherParametersTable').innerHTML = '';
        
        allRows.forEach(row => {
            const paramName = row.cells[0]?.textContent;
            if (!paramName) return;
            
            const clonedRow = row.cloneNode(true);
            
            if (timeParams.includes(paramName)) {
                document.getElementById('timeParametersTable').appendChild(clonedRow);
            } else if (powerParams.includes(paramName)) {
                document.getElementById('powerParametersTable').appendChild(clonedRow);
            } else {
                document.getElementById('otherParametersTable').appendChild(clonedRow);
            }
        });
    }
    
    // 更新参数计数
    function updateParameterCount() {
        const paramCount = document.querySelectorAll('#allParametersTable tr').length;
        document.getElementById('param-count').textContent = paramCount;
    }
    
    // 查询参数（异步版本）
    function queryParameters() {
        const paramElements = document.querySelectorAll('[id^="REG_"]');
        paramElements.forEach(el => {
            el.textContent = '查询中...';
        });
        
        fetch('/api/device/{{ device.id }}/async/parameters', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                pollTaskStatus(data.task_id, 'parameters', (result) => {
                    if (result.success && result.parameters) {
                        Object.entries(result.parameters).forEach(([paramName, paramData]) => {
                            const element = document.getElementById(paramName);
                            if (element && paramData && typeof paramData === 'object' && 'value' in paramData) {
                                element.textContent = paramData.value;
                            }
                        });
                        updateParameterCategories();
                        showNotification('参数查询完成', 'success');
                    } else {
                        throw new Error(result.error || '查询失败');
                    }
                }, (error) => {
                    console.error('获取参数失败:', error);
                    showNotification('获取参数失败: ' + error, 'error');
                    paramElements.forEach(el => {
                        el.textContent = '--';
                    });
                });
            } else {
                throw new Error(data.error || '启动查询任务失败');
            }
        })
        .catch(error => {
            console.error('查询参数失败:', error);
            showNotification('查询参数失败: ' + error, 'error');
            paramElements.forEach(el => {
                el.textContent = '--';
            });
        });
    }
    
    // 编辑参数
    function editParameter(paramName, paramAddr) {
        document.getElementById('paramName').value = paramName;
        document.getElementById('paramAddr').value = paramAddr;
        document.getElementById('paramValue').value = '';
        
        // 获取参数描述
        const descElement = document.querySelector(`#allParametersTable tr td:first-child`);
        let description = '无描述';
        document.querySelectorAll('#allParametersTable tr').forEach(row => {
            if (row.cells[0]?.textContent === paramName) {
                description = row.cells[3]?.textContent || '无描述';
            }
        });
        document.getElementById('paramDescription').textContent = description;
        
        // 更新模态框标题
        document.getElementById('editParameterModalLabel').textContent = `编辑参数 - ${paramName}`;
        
        const modal = new bootstrap.Modal(document.getElementById('editParameterModal'));
        modal.show();
    }
    
    // 保存参数
    function saveParameter() {
        const paramName = document.getElementById('paramName').value;
        const paramAddr = document.getElementById('paramAddr').value;
        const paramValue = document.getElementById('paramValue').value;
        
        if (!paramValue) {
            alert('请输入参数值');
            return;
        }
        
        fetch('/api/device/{{ device.id }}/parameters', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                reg_addr: paramAddr,
                reg_value: paramValue
            }),
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('参数保存成功', 'success');
                document.getElementById(paramName).textContent = paramValue;
                $('#editParameterModal').modal('hide');
            } else {
                throw new Error(data.error || '保存失败');
            }
        })
        .catch(error => {
            console.error('保存参数失败:', error);
            showNotification('保存参数失败: ' + error, 'error');
        });
    }
    
    // 其他功能函数（查询调试信息、固件信息等）
    function queryDebugInfo() {
        // 实现查询调试信息功能
        showNotification('查询调试信息功能开发中...', 'info');
    }
    
    function queryFirmwareInfo() {
        // 实现查询固件信息功能
        showNotification('查询固件信息功能开发中...', 'info');
    }
    
    function queryErrorCounts() {
        // 实现查询错误计数功能
        showNotification('查询错误计数功能开发中...', 'info');
    }
    
    function queryDeviceLocation() {
        // 实现查询设备位置功能
        showNotification('查询设备位置功能开发中...', 'info');
    }
    
    function openDebugScriptModal() {
        // 实现打开调试脚本模态框功能
        showNotification('调试脚本功能开发中...', 'info');
    }
    
    function querySimInfo() {
        // 实现查询SIM卡信息功能
        showNotification('查询SIM卡信息功能开发中...', 'info');
    }
    
    // 轮询任务状态的辅助函数
    function pollTaskStatus(taskId, taskType, onSuccess, onError) {
        const pollInterval = setInterval(() => {
            fetch(`/api/device/{{ device.id }}/async/status/${taskId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'completed') {
                        clearInterval(pollInterval);
                        onSuccess(data.result);
                    } else if (data.status === 'failed') {
                        clearInterval(pollInterval);
                        onError(data.error || '任务执行失败');
                    }
                    // 如果状态是 'running'，继续轮询
                })
                .catch(error => {
                    clearInterval(pollInterval);
                    onError(error.message);
                });
        }, 1000); // 每秒轮询一次
        
        // 设置超时，避免无限轮询
        setTimeout(() => {
            clearInterval(pollInterval);
            onError('任务执行超时');
        }, 30000); // 30秒超时
    }
</script>
{% endblock %}
