{% extends "base_new.html" %}

{# 导入宏 #}
{% from 'macros/ui_components.html' import page_header, card_container, stat_card, button %}

{% block title %}系统总览{% endblock %}

{% block content %}
<div class="mt-4">
    {# 页面标题 #}
    {{ page_header("系统总览", "tachometer-alt", "充电桩设备管理系统概览",
        button("刷新数据", onclick="refreshDashboard()", color="primary", icon="sync-alt") +
        button("系统设置", onclick="window.location.href='/settings'", color="outline-secondary", icon="cogs")
    ) }}

    {# 统计卡片 #}
    <div class="row mb-4">
        {{ stat_card("总设备数", total_devices|default(0), "microchip", "primary", id="totalDevicesCount") }}
        {{ stat_card("在线设备", online_devices|default(0), "wifi", "success", id="onlineDevicesCount") }}
        {{ stat_card("离线设备", offline_devices|default(0), "times-circle", "danger", id="offlineDevicesCount") }}
        {{ stat_card("今日新增", today_new_devices|default(0), "plus-circle", "info") }}
    </div>

    <div class="row mb-4">
        {{ stat_card("固件总数", total_firmwares|default(0), "file-code", "secondary") }}
        {{ stat_card("OTA任务", total_ota_tasks|default(0), "tasks", "warning") }}
        {{ stat_card("成功率", ota_success_rate|default("0%"), "check-circle", "success") }}
        {{ stat_card("系统运行", system_uptime|default("0天"), "clock", "info") }}
    </div>

    {# 快速操作和最近活动 #}
    <div class="row">
        <div class="col-lg-6 mb-4">
            {% call card_container("快速操作", "bolt") %}
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="d-grid">
                            <a href="{{ url_for('device.add_device') }}" class="btn btn-primary">
                                <i class="fas fa-plus fa-2x mb-2 d-block"></i>
                                添加设备
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-grid">
                            <a href="{{ url_for('firmware.upload_firmware') }}" class="btn btn-success">
                                <i class="fas fa-upload fa-2x mb-2 d-block"></i>
                                上传固件
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-grid">
                            <a href="{{ url_for('batch_ota.batch_ota_page') }}" class="btn btn-warning">
                                <i class="fas fa-rocket fa-2x mb-2 d-block"></i>
                                批量OTA
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-grid">
                            <a href="{{ url_for('device_location.device_map') }}" class="btn btn-info">
                                <i class="fas fa-map-marker-alt fa-2x mb-2 d-block"></i>
                                设备地图
                            </a>
                        </div>
                    </div>
                </div>
            {% endcall %}
        </div>

        <div class="col-lg-6 mb-4">
            {% call card_container("系统状态", "server") %}
                <div class="row g-3">
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 mb-0 text-success" id="cpuUsage">{{ cpu_usage|default("0%") }}</div>
                            <small class="text-muted">CPU使用率</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 mb-0 text-info" id="memoryUsage">{{ memory_usage|default("0%") }}</div>
                            <small class="text-muted">内存使用率</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 mb-0 text-warning" id="diskUsage">{{ disk_usage|default("0%") }}</div>
                            <small class="text-muted">磁盘使用率</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 mb-0 text-primary" id="networkStatus">
                                <i class="fas fa-{{ 'check-circle' if network_status else 'times-circle' }}"></i>
                            </div>
                            <small class="text-muted">网络状态</small>
                        </div>
                    </div>
                </div>
            {% endcall %}
        </div>
    </div>

    {# 最近活动和设备状态 #}
    <div class="row">
        <div class="col-lg-8 mb-4">
            {% call card_container("最近活动", "history") %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>类型</th>
                                <th>设备</th>
                                <th>描述</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody id="recentActivities">
                            {% if recent_activities %}
                                {% for activity in recent_activities %}
                                <tr>
                                    <td>{{ activity.created_at.strftime('%H:%M:%S') }}</td>
                                    <td>
                                        <span class="badge bg-{{ activity.type_color }}">{{ activity.type_name }}</span>
                                    </td>
                                    <td>{{ activity.device_id }}</td>
                                    <td>{{ activity.description }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if activity.success else 'danger' }}">
                                            {{ '成功' if activity.success else '失败' }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                            <tr>
                                <td colspan="5" class="text-center text-muted">
                                    <i class="fas fa-info-circle me-2"></i>暂无最近活动
                                </td>
                            </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            {% endcall %}
        </div>

        <div class="col-lg-4 mb-4">
            {% call card_container("设备状态分布", "chart-pie") %}
                <div class="text-center">
                    <canvas id="deviceStatusChart" width="200" height="200"></canvas>
                </div>
                <div class="mt-3">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="text-success">
                                <i class="fas fa-circle me-1"></i>在线
                                <div class="fw-bold" id="onlinePercentage">{{ online_percentage|default("0%") }}</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-danger">
                                <i class="fas fa-circle me-1"></i>离线
                                <div class="fw-bold" id="offlinePercentage">{{ offline_percentage|default("0%") }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            {% endcall %}
        </div>
    </div>

    {# 最新设备和固件 #}
    <div class="row">
        <div class="col-lg-6 mb-4">
            {% call card_container("最新设备", "microchip") %}
                <div class="list-group list-group-flush">
                    {% if latest_devices %}
                        {% for device in latest_devices %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ device.device_id }}</h6>
                                <p class="mb-1 text-muted">{{ device.device_remark or '无备注' }}</p>
                                <small class="text-muted">{{ device.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                            </div>
                            <span class="badge bg-{{ 'success' if device.is_online else 'danger' }} rounded-pill">
                                {{ '在线' if device.is_online else '离线' }}
                            </span>
                        </div>
                        {% endfor %}
                    {% else %}
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-info-circle me-2"></i>暂无设备
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer bg-transparent">
                    <a href="{{ url_for('device.devices') }}" class="btn btn-sm btn-outline-primary">
                        查看全部设备 <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            {% endcall %}
        </div>

        <div class="col-lg-6 mb-4">
            {% call card_container("最新固件", "file-code") %}
                <div class="list-group list-group-flush">
                    {% if latest_firmwares %}
                        {% for firmware in latest_firmwares %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ firmware.firmware_name }}</h6>
                                <p class="mb-1 text-muted">版本: {{ firmware.version }}</p>
                                <small class="text-muted">{{ firmware.upload_time.strftime('%Y-%m-%d %H:%M') }}</small>
                            </div>
                            <span class="badge bg-info rounded-pill">
                                {{ firmware.file_size_mb }}MB
                            </span>
                        </div>
                        {% endfor %}
                    {% else %}
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-info-circle me-2"></i>暂无固件
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer bg-transparent">
                    <a href="{{ url_for('firmware.firmware_list') }}" class="btn btn-sm btn-outline-primary">
                        查看全部固件 <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            {% endcall %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // 页面加载时初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化设备状态图表
        initDeviceStatusChart();
        
        // 启动数据刷新
        startDataRefresh();
        
        // 获取最新活动
        fetchRecentActivities();
    });
    
    // 初始化设备状态饼图
    function initDeviceStatusChart() {
        const ctx = document.getElementById('deviceStatusChart').getContext('2d');
        const onlineDevices = {{ online_devices|default(0) }};
        const offlineDevices = {{ offline_devices|default(0) }};
        
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['在线设备', '离线设备'],
                datasets: [{
                    data: [onlineDevices, offlineDevices],
                    backgroundColor: ['#28a745', '#dc3545'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }
    
    // 刷新仪表板数据
    function refreshDashboard() {
        fetch('/api/dashboard/stats')
            .then(response => response.json())
            .then(data => {
                // 更新统计数据
                document.getElementById('totalDevicesCount').textContent = data.total_devices;
                document.getElementById('onlineDevicesCount').textContent = data.online_devices;
                document.getElementById('offlineDevicesCount').textContent = data.offline_devices;
                
                // 更新系统状态
                document.getElementById('cpuUsage').textContent = data.cpu_usage + '%';
                document.getElementById('memoryUsage').textContent = data.memory_usage + '%';
                document.getElementById('diskUsage').textContent = data.disk_usage + '%';
                
                showNotification('数据已刷新', 'success');
            })
            .catch(error => {
                console.error('刷新数据失败:', error);
                showNotification('刷新数据失败', 'error');
            });
    }
    
    // 获取最近活动
    function fetchRecentActivities() {
        fetch('/api/dashboard/recent_activities')
            .then(response => response.json())
            .then(data => {
                const tbody = document.getElementById('recentActivities');
                if (data.activities && data.activities.length > 0) {
                    tbody.innerHTML = data.activities.map(activity => `
                        <tr>
                            <td>${activity.time}</td>
                            <td><span class="badge bg-${activity.type_color}">${activity.type_name}</span></td>
                            <td>${activity.device_id}</td>
                            <td>${activity.description}</td>
                            <td><span class="badge bg-${activity.success ? 'success' : 'danger'}">${activity.success ? '成功' : '失败'}</span></td>
                        </tr>
                    `).join('');
                }
            })
            .catch(error => {
                console.error('获取最近活动失败:', error);
            });
    }
    
    // 启动数据刷新
    function startDataRefresh() {
        // 每分钟刷新一次数据
        setInterval(() => {
            fetchDeviceStatus();
            fetchRecentActivities();
        }, 60000);
    }
</script>
{% endblock %}
