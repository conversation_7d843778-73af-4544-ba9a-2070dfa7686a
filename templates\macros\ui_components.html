{# UI组件宏定义文件 #}

{# 统计卡片宏 #}
{% macro stat_card(title, value, icon, color="primary", description="", id="") %}
<div class="col-md-{{ 12 // 4 if caller is not defined else caller() }}">
    <div class="card border-{{ color }} h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-{{ icon }} fa-3x text-{{ color }}"></i>
                </div>
                <div class="flex-grow-1 ms-3">
                    <h6 class="card-title text-muted mb-1">{{ title }}</h6>
                    <h3 class="mb-0" {% if id %}id="{{ id }}"{% endif %}>{{ value }}</h3>
                    {% if description %}
                    <p class="text-muted mb-0 small">{{ description }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endmacro %}

{# 页面标题宏 #}
{% macro page_header(title, icon="", subtitle="", actions="") %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center flex-wrap gap-3">
            <div>
                <h2 class="mb-0">
                    {% if icon %}<i class="fas fa-{{ icon }} text-primary"></i> {% endif %}
                    {{ title }}
                </h2>
                {% if subtitle %}
                <p class="text-muted mb-0">{{ subtitle }}</p>
                {% endif %}
            </div>
            {% if actions %}
            <div class="d-flex gap-2 flex-wrap">
                {{ actions }}
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endmacro %}

{# 卡片容器宏 #}
{% macro card_container(title="", icon="", header_actions="", body_class="", card_class="shadow-sm") %}
<div class="card {{ card_class }}">
    {% if title %}
    <div class="card-header bg-white py-3">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="mb-0">
                    {% if icon %}<i class="fas fa-{{ icon }} text-primary"></i> {% endif %}
                    {{ title }}
                </h5>
            </div>
            {% if header_actions %}
            <div class="col-auto">
                {{ header_actions }}
            </div>
            {% endif %}
        </div>
        {{ caller() if caller }}
    </div>
    {% endif %}
    <div class="card-body {{ body_class }}">
        {% if not caller %}
            {{ content if content is defined else "" }}
        {% else %}
            {{ caller() }}
        {% endif %}
    </div>
</div>
{% endmacro %}

{# 筛选输入组宏 #}
{% macro filter_input(id, placeholder, icon="search", type="text", options=none) %}
<div class="input-group" style="width: 160px;">
    <span class="input-group-text"><i class="fas fa-{{ icon }}"></i></span>
    {% if options %}
    <select class="form-select" id="{{ id }}">
        {% for value, label in options %}
        <option value="{{ value }}">{{ label }}</option>
        {% endfor %}
    </select>
    {% else %}
    <input type="{{ type }}" class="form-control" id="{{ id }}" placeholder="{{ placeholder }}">
    {% endif %}
</div>
{% endmacro %}

{# 按钮宏 #}
{% macro button(text, onclick="", type="button", color="primary", size="", icon="", disabled=false, id="", class="", title="") %}
<button type="{{ type }}" 
        class="btn btn-{{ color }}{% if size %} btn-{{ size }}{% endif %}{{ ' ' + class if class }}" 
        {% if onclick %}onclick="{{ onclick }}"{% endif %}
        {% if disabled %}disabled{% endif %}
        {% if id %}id="{{ id }}"{% endif %}
        {% if title %}title="{{ title }}"{% endif %}>
    {% if icon %}<i class="fas fa-{{ icon }} me-1"></i>{% endif %}
    {{ text }}
</button>
{% endmacro %}

{# 状态徽章宏 #}
{% macro status_badge(status, online_text="在线", offline_text="离线", online_class="bg-success-subtle text-success", offline_class="bg-danger-subtle text-danger") %}
<span class="badge {{ online_class if status else offline_class }}">
    <i class="fas fa-circle me-1"></i>{{ online_text if status else offline_text }}
</span>
{% endmacro %}

{# 加载指示器宏 #}
{% macro loading_indicator(text="加载中...", size="", id="") %}
<div class="text-center p-4" {% if id %}id="{{ id }}"{% endif %}>
    <div class="spinner-border text-primary{% if size %} spinner-border-{{ size }}{% endif %}" role="status">
        <span class="visually-hidden">{{ text }}</span>
    </div>
    <div class="mt-2">{{ text }}</div>
</div>
{% endmacro %}

{# 模态框宏 #}
{% macro modal(id, title, size="", body_content="", footer_content="", icon="") %}
<div class="modal fade" id="{{ id }}" tabindex="-1" aria-labelledby="{{ id }}Label" aria-hidden="true">
    <div class="modal-dialog{% if size %} modal-{{ size }}{% endif %}">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="{{ id }}Label">
                    {% if icon %}<i class="fas fa-{{ icon }} text-primary me-2"></i>{% endif %}
                    {{ title }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                {% if caller %}
                    {{ caller() }}
                {% else %}
                    {{ body_content }}
                {% endif %}
            </div>
            {% if footer_content %}
            <div class="modal-footer">
                {{ footer_content }}
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endmacro %}

{# 表单输入组宏 #}
{% macro form_input(name, label, type="text", placeholder="", icon="", required=false, value="", help_text="") %}
<div class="mb-3">
    <label for="{{ name }}" class="form-label">{{ label }}</label>
    <div class="input-group">
        {% if icon %}
        <span class="input-group-text"><i class="fas fa-{{ icon }}"></i></span>
        {% endif %}
        <input type="{{ type }}" 
               class="form-control" 
               id="{{ name }}" 
               name="{{ name }}" 
               {% if placeholder %}placeholder="{{ placeholder }}"{% endif %}
               {% if required %}required{% endif %}
               {% if value %}value="{{ value }}"{% endif %}>
    </div>
    {% if help_text %}
    <div class="form-text">{{ help_text }}</div>
    {% endif %}
</div>
{% endmacro %}

{# 表单选择框宏 #}
{% macro form_select(name, label, options, icon="", required=false, selected="", help_text="") %}
<div class="mb-3">
    <label for="{{ name }}" class="form-label">{{ label }}</label>
    <div class="input-group">
        {% if icon %}
        <span class="input-group-text"><i class="fas fa-{{ icon }}"></i></span>
        {% endif %}
        <select class="form-select" id="{{ name }}" name="{{ name }}" {% if required %}required{% endif %}>
            {% for value, text in options %}
            <option value="{{ value }}" {% if value == selected %}selected{% endif %}>{{ text }}</option>
            {% endfor %}
        </select>
    </div>
    {% if help_text %}
    <div class="form-text">{{ help_text }}</div>
    {% endif %}
</div>
{% endmacro %}

{# 分页控件宏 #}
{% macro pagination(current_page, total_pages, base_url="", page_param="page") %}
<nav aria-label="分页导航">
    <ul class="pagination pagination-lg mb-0">
        <li class="page-item {% if current_page <= 1 %}disabled{% endif %}">
            <a class="page-link" href="{% if current_page > 1 %}{{ base_url }}?{{ page_param }}={{ current_page - 1 }}{% else %}#{% endif %}">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
        
        {% for page in range(1, total_pages + 1) %}
            {% if page == current_page %}
            <li class="page-item active">
                <span class="page-link">{{ page }}</span>
            </li>
            {% elif page <= 3 or page >= total_pages - 2 or (page >= current_page - 1 and page <= current_page + 1) %}
            <li class="page-item">
                <a class="page-link" href="{{ base_url }}?{{ page_param }}={{ page }}">{{ page }}</a>
            </li>
            {% elif page == 4 and current_page > 6 %}
            <li class="page-item disabled">
                <span class="page-link">...</span>
            </li>
            {% elif page == total_pages - 3 and current_page < total_pages - 5 %}
            <li class="page-item disabled">
                <span class="page-link">...</span>
            </li>
            {% endif %}
        {% endfor %}
        
        <li class="page-item {% if current_page >= total_pages %}disabled{% endif %}">
            <a class="page-link" href="{% if current_page < total_pages %}{{ base_url }}?{{ page_param }}={{ current_page + 1 }}{% else %}#{% endif %}">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
    </ul>
</nav>
{% endmacro %}
